﻿@model IEnumerable<ViVu.Models.Product>

@{
    ViewData["Title"] = "Trang chủ";
}

<!-- Destination Showcase - Main Container -->
<div id="destination-showcase-container" class="full-page">
    <!-- Destination Showcase - Asia -->
    <div class="destination-showcase asia">
        <div class="destination-container">
            <!-- Main content area -->
            <div class="showcase-content">
                <!-- Left side with continent info -->
                <div class="showcase-info">
                    <h1 class="continent-name">Asia</h1>
                    <p class="continent-description">Asia is a continent full of unique, powerful, exotic and spiritually rich and beautiful landscapes for centuries. Feel a free, bustling and connected atmosphere for travelers.</p>
                    <a href="/Explore/Asia" class="explore-btn">Explore</a>
                </div>

                <!-- Right side with destination cards -->
                <div class="showcase-destinations">
                    <!-- Large destination card -->
                    <div class="destination-card destination-card-large">
                        <img src="/images/banners/hinh-anh-ben-tre-tho-mong-tru-tinh_022742052.jpg" alt="Kalanggong Beach, Thailand">
                        <div class="card-actions">
                            <div class="card-action-btn">
                                <i class="bi bi-heart"></i>
                            </div>
                            <div class="card-action-btn">
                                <i class="bi bi-share"></i>
                            </div>
                        </div>
                        <div class="destination-card-overlay">
                            <h3 class="destination-card-title">Kalanggong Beach</h3>
                            <p class="destination-card-subtitle">Thailand</p>
                        </div>
                    </div>

                    <!-- Small destination cards -->
                    <div class="destination-card destination-card-small">
                        <img src="/images/banners/hinh-anh-chieu-hoang-hon-o-ben-tre_022743590.jpg" alt="Tea plantation, Sri Lanka">
                        <div class="card-actions">
                            <div class="card-action-btn">
                                <i class="bi bi-heart"></i>
                            </div>
                        </div>
                        <div class="destination-card-overlay">
                            <h3 class="destination-card-title">Tea plantation</h3>
                            <p class="destination-card-subtitle">Sri Lanka</p>
                        </div>
                    </div>

                    <div class="destination-card destination-card-small">
                        <img src="/images/banners/banner_home_02.jpg" alt="An Phu Hung Hotel">
                        <div class="card-actions">
                            <div class="card-action-btn">
                                <i class="bi bi-heart"></i>
                            </div>
                        </div>
                        <div class="destination-card-overlay">
                            <h3 class="destination-card-title">An Phu Hung Hotel</h3>
                            <p class="destination-card-subtitle">Vietnam</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom navigation controls -->
            <div class="showcase-controls">
                <div class="continent-nav">
                    <div class="nav-btn">
                        <i class="bi bi-arrow-left"></i>
                    </div>
                    <span>Africa</span>
                </div>

                <div class="continent-indicators">
                    <div class="continent-indicator"></div>
                    <div class="continent-indicator active"></div>
                    <div class="continent-indicator"></div>
                    <div class="continent-indicator"></div>
                    <div class="continent-indicator"></div>
                </div>

                <div class="continent-nav">
                    <span>Australia</span>
                    <div class="nav-btn">
                        <i class="bi bi-arrow-right"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Modal -->
<div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title" id="searchModalLabel">Tìm kiếm</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="/Search" method="get">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" placeholder="Nhập từ khóa tìm kiếm..." name="q" required>
                        <button class="btn btn-modern btn-modern-primary" type="submit">
                            <i class="bi bi-search me-1"></i> Tìm
                        </button>
                    </div>
                    <div class="d-flex flex-wrap gap-2">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="tours" id="searchTours" name="type" checked>
                            <label class="form-check-label" for="searchTours">
                                Tours
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="hotels" id="searchHotels" name="type" checked>
                            <label class="form-check-label" for="searchHotels">
                                Khách sạn
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="destinations" id="searchDestinations" name="type" checked>
                            <label class="form-check-label" for="searchDestinations">
                                Địa điểm
                            </label>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Navigation Tabs -->
<div class="container-fluid px-3 px-md-5 mb-5">
    <div class="homepage-tabs-container mx-auto" style="max-width: 1200px;">
        <!-- Main Navigation Tabs -->
        <div class="homepage-main-tabs bg-white rounded-top">
            <ul class="nav nav-tabs border-0 justify-content-center" id="homepageTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active d-flex align-items-center" id="search-main-tab" data-bs-toggle="tab" data-bs-target="#search-content" type="button" role="tab" aria-controls="search-content" aria-selected="true">
                        <i class="bi bi-search me-2"></i> Tìm kiếm
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center" id="destinations-main-tab" data-bs-toggle="tab" data-bs-target="#destinations-content" type="button" role="tab" aria-controls="destinations-content" aria-selected="false">
                        <i class="bi bi-geo-alt me-2"></i> Điểm đến
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center" id="accommodations-main-tab" data-bs-toggle="tab" data-bs-target="#accommodations-content" type="button" role="tab" aria-controls="accommodations-content" aria-selected="false">
                        <i class="bi bi-building me-2"></i> Khách sạn
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center" id="tours-main-tab" data-bs-toggle="tab" data-bs-target="#tours-content" type="button" role="tab" aria-controls="tours-content" aria-selected="false">
                        <i class="bi bi-compass me-2"></i> Tour du lịch
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center" id="experiences-main-tab" data-bs-toggle="tab" data-bs-target="#experiences-content" type="button" role="tab" aria-controls="experiences-content" aria-selected="false">
                        <i class="bi bi-stars me-2"></i> Trải nghiệm
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link d-flex align-items-center" id="about-main-tab" data-bs-toggle="tab" data-bs-target="#about-content" type="button" role="tab" aria-controls="about-content" aria-selected="false">
                        <i class="bi bi-info-circle me-2"></i> Giới thiệu
                    </button>
                </li>
            </ul>
        </div>

        <!-- Tab Content Container -->
        <div class="tab-content homepage-tab-content" id="homepageTabsContent">
            <!-- Search Tab Content -->
            <div class="tab-pane fade show active" id="search-content" role="tabpanel" aria-labelledby="search-main-tab">
                <div class="search-form-modern">
                    <!-- Search Tabs -->
                    <div class="search-tabs bg-white">
                        <ul class="nav nav-tabs border-0" id="searchTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active d-flex align-items-center" id="hotel-tab" data-bs-toggle="tab" data-bs-target="#hotel-search" type="button" role="tab" aria-controls="hotel-search" aria-selected="true">
                                    <i class="bi bi-building me-2"></i> Khách sạn
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center" id="tour-tab" data-bs-toggle="tab" data-bs-target="#tour-search" type="button" role="tab" aria-controls="tour-search" aria-selected="false">
                                    <i class="bi bi-compass me-2"></i> Tour du lịch
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center" id="service-tab" data-bs-toggle="tab" data-bs-target="#service-search" type="button" role="tab" aria-controls="service-search" aria-selected="false">
                                    <i class="bi bi-stars me-2"></i> Dịch vụ
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity-search" type="button" role="tab" aria-controls="activity-search" aria-selected="false">
                                    <i class="bi bi-calendar-event me-2"></i> Hoạt động
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center" id="combo-tab" data-bs-toggle="tab" data-bs-target="#combo-search" type="button" role="tab" aria-controls="combo-search" aria-selected="false">
                                    <i class="bi bi-box me-2"></i> Combo tiết kiệm
                                </button>
                            </li>
                        </ul>
                    </div>

                    <!-- Tab Content -->
                    <div class="tab-content p-4" id="searchTabsContent">
                        <!-- Hotel Search Tab -->
                        <div class="tab-pane fade show active" id="hotel-search" role="tabpanel" aria-labelledby="hotel-tab">
                            <form asp-controller="Search" asp-action="Search" method="post">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label class="form-label fw-bold"><i class="bi bi-geo-alt me-1 text-primary"></i>Địa điểm</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-white"><i class="bi bi-search"></i></span>
                                            <select name="locationId" class="form-select shadow-sm" asp-items="ViewBag.Locations">
                                                <option value="">-- Chọn địa điểm --</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label fw-bold"><i class="bi bi-calendar-check me-1 text-primary"></i>Ngày nhận phòng</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                            <input type="date" name="checkIn" class="form-control shadow-sm" value="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label fw-bold"><i class="bi bi-calendar-x me-1 text-primary"></i>Ngày trả phòng</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                            <input type="date" name="checkOut" class="form-control shadow-sm" value="@DateTime.Today.AddDays(2).ToString("yyyy-MM-dd")">
                                        </div>
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button type="submit" class="btn btn-modern btn-modern-primary w-100">
                                            <i class="bi bi-search me-1"></i> TÌM
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Tour Search Tab -->
                        <div class="tab-pane fade" id="tour-search" role="tabpanel" aria-labelledby="tour-tab">
                            <form asp-controller="Tour" asp-action="Search" method="post">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label class="form-label fw-bold"><i class="bi bi-geo-alt me-1 text-primary"></i>Điểm đến</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-white"><i class="bi bi-search"></i></span>
                                            <select name="locationId" class="form-select shadow-sm" asp-items="ViewBag.Locations">
                                                <option value="">-- Chọn điểm đến --</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label fw-bold"><i class="bi bi-calendar-check me-1 text-primary"></i>Ngày bắt đầu</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                            <input type="date" name="startDate" class="form-control shadow-sm" value="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label fw-bold"><i class="bi bi-people me-1 text-primary"></i>Số người</label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-white"><i class="bi bi-person"></i></span>
                                            <select name="groupSize" class="form-select shadow-sm">
                                                <option value="1">1 người</option>
                                                <option value="2" selected>2 người</option>
                                                <option value="3">3 người</option>
                                                <option value="4">4 người</option>
                                                <option value="5">5 người</option>
                                                <option value="6">6+ người</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button type="submit" class="btn btn-modern btn-modern-primary w-100">
                                            <i class="bi bi-search me-1"></i> TÌM
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>

            <!-- Service Search Tab -->
            <div class="tab-pane fade" id="service-search" role="tabpanel" aria-labelledby="service-tab">
                <form asp-controller="Service" asp-action="Index" method="get">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label fw-bold"><i class="bi bi-geo-alt me-1 text-primary"></i>Địa điểm</label>
                            <div class="input-group">
                                <span class="input-group-text bg-white"><i class="bi bi-search"></i></span>
                                <select name="locationId" class="form-select shadow-sm" asp-items="ViewBag.Locations">
                                    <option value="">-- Chọn địa điểm --</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold"><i class="bi bi-calendar-check me-1 text-primary"></i>Ngày sử dụng</label>
                            <div class="input-group">
                                <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                <input type="date" name="serviceDate" class="form-control shadow-sm" value="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold"><i class="bi bi-search me-1 text-primary"></i>Tìm kiếm</label>
                            <div class="input-group">
                                <span class="input-group-text bg-white"><i class="bi bi-search"></i></span>
                                <input type="text" name="searchTerm" class="form-control shadow-sm" placeholder="Tên dịch vụ...">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-modern btn-modern-primary w-100">
                                <i class="bi bi-search me-1"></i> TÌM
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Activity Search Tab -->
            <div class="tab-pane fade" id="activity-search" role="tabpanel" aria-labelledby="activity-tab">
                <form asp-controller="Activity" asp-action="Search" method="post">
                    <div class="row g-3">
                        <div class="col-md-5">
                            <label class="form-label fw-bold"><i class="bi bi-geo-alt me-1 text-primary"></i>Địa điểm</label>
                            <div class="input-group">
                                <span class="input-group-text bg-white"><i class="bi bi-search"></i></span>
                                <select name="locationId" class="form-select shadow-sm" asp-items="ViewBag.Locations">
                                    <option value="">-- Chọn địa điểm --</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label fw-bold"><i class="bi bi-calendar-check me-1 text-primary"></i>Ngày tham gia</label>
                            <div class="input-group">
                                <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                <input type="date" name="activityDate" class="form-control shadow-sm" value="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")">
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-modern btn-modern-primary w-100">
                                <i class="bi bi-search me-1"></i> TÌM
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Combo Search Tab -->
            <div class="tab-pane fade" id="combo-search" role="tabpanel" aria-labelledby="combo-tab">
                <form asp-controller="Combo" asp-action="Search" method="post">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label fw-bold"><i class="bi bi-geo-alt me-1 text-primary"></i>Điểm đến</label>
                            <div class="input-group">
                                <span class="input-group-text bg-white"><i class="bi bi-search"></i></span>
                                <select name="locationId" class="form-select shadow-sm" asp-items="ViewBag.Locations">
                                    <option value="">-- Chọn điểm đến --</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold"><i class="bi bi-calendar-check me-1 text-primary"></i>Ngày đi</label>
                            <div class="input-group">
                                <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                <input type="date" name="startDate" class="form-control shadow-sm" value="@DateTime.Today.AddDays(1).ToString("yyyy-MM-dd")">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label fw-bold"><i class="bi bi-calendar-x me-1 text-primary"></i>Ngày về</label>
                            <div class="input-group">
                                <span class="input-group-text bg-white"><i class="bi bi-calendar"></i></span>
                                <input type="date" name="endDate" class="form-control shadow-sm" value="@DateTime.Today.AddDays(3).ToString("yyyy-MM-dd")">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label fw-bold"><i class="bi bi-people me-1 text-primary"></i>Số người</label>
                            <div class="input-group">
                                <span class="input-group-text bg-white"><i class="bi bi-person"></i></span>
                                <select name="groupSize" class="form-select shadow-sm">
                                    <option value="1">1 người</option>
                                    <option value="2" selected>2 người</option>
                                    <option value="3">3 người</option>
                                    <option value="4">4 người</option>
                                    <option value="5">5+ người</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-modern btn-modern-primary w-100">
                                <i class="bi bi-search me-1"></i> TÌM
                            </button>
                        </div>
                    </div>
                </form>
            </div>
                </div>
            </div>

            <!-- Destinations Tab Content -->
            <div class="tab-pane fade" id="destinations-content" role="tabpanel" aria-labelledby="destinations-main-tab">
                <div class="section-title" data-aos="fade-up">
                    <h2>Điểm đến nổi bật</h2>
                </div>

    <!-- Large Featured Destinations -->
    <div class="row mb-4">
        <div class="col-12" data-aos="fade-up" data-aos-delay="100">
            <div class="featured-destinations-large">
                <div class="row g-3">
                    <div class="col-md-8">
                        <div class="position-relative destination-card">
                            <img src="/images/banners/banner_home_01.jpg" class="img-fluid w-100 rounded" style="height: 400px; object-fit: cover;" alt="Bến Tre">
                            <div class="position-absolute bottom-0 start-0 w-100 p-4 text-white" style="background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);">
                                <h3 class="mb-2 text-shadow">Bến Tre</h3>
                                <p class="mb-2">Khám phá vẻ đẹp của xứ dừa với những khu vườn xanh mát và văn hóa đặc sắc</p>
                                <a asp-controller="Explore" asp-action="BenTre" class="btn btn-modern btn-modern-primary">Khám phá ngay</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="row g-3">
                            <div class="col-12">
                                <div class="position-relative destination-card">
                                    <img src="/images/banners/banner_home_02.jpg" class="img-fluid w-100 rounded" style="height: 190px; object-fit: cover;" alt="Hoàng hôn Bến Tre">
                                    <div class="position-absolute bottom-0 start-0 w-100 p-3 text-white" style="background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);">
                                        <h5 class="mb-1 text-shadow">Hoàng hôn Bến Tre</h5>
                                        <a href="#" class="btn btn-sm btn-modern btn-modern-primary">Xem chi tiết</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="position-relative destination-card">
                                    <img src="/images/experiences/exp_lang-hoa-cho-lach_01.jpg" class="img-fluid w-100 rounded" style="height: 190px; object-fit: cover;" alt="Làng hoa Chợ Lách">
                                    <div class="position-absolute bottom-0 start-0 w-100 p-3 text-white" style="background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0) 100%);">
                                        <h5 class="mb-1 text-shadow">Làng hoa Chợ Lách</h5>
                                        <a href="#" class="btn btn-sm btn-modern btn-modern-primary">Xem chi tiết</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Regular Destinations Grid -->
    <div class="row">
        @if (ViewBag.FeaturedLocations != null)
        {
            foreach (var location in ViewBag.FeaturedLocations)
            {
                <div class="col-md-4 mb-4" data-aos="fade-up">
                    <div class="vertical-destination-card">
                        <img src="@(string.IsNullOrEmpty(location.ImageUrl) ? "/images/default-location.jpg" : location.ImageUrl)"
                             alt="@location.Name">
                        <div class="card-actions">
                            <div class="card-action-btn">
                                <i class="bi bi-heart"></i>
                            </div>
                            <div class="card-action-btn">
                                <i class="bi bi-share"></i>
                            </div>
                        </div>
                        <div class="position-absolute top-0 start-0 m-3">
                            <div class="d-flex">
                                <span class="badge bg-primary me-2">Nổi bật</span>
                            </div>
                        </div>
                        <div class="vertical-destination-card-overlay">
                            <h3 class="destination-card-title">@location.Name</h3>
                            <p class="destination-card-subtitle">
                                <i class="bi bi-geo-alt-fill"></i> @location.City?.Name
                            </p>
                            <div class="mt-2">
                                <div class="mb-2">
                                    <i class="bi bi-star-fill text-warning"></i>
                                    <i class="bi bi-star-fill text-warning"></i>
                                    <i class="bi bi-star-fill text-warning"></i>
                                    <i class="bi bi-star-fill text-warning"></i>
                                    <i class="bi bi-star-half text-warning"></i>
                                </div>
                                <p class="card-text text-white-50">@(location.Description?.Length > 80 ? location.Description.Substring(0, 80) + "..." : location.Description)</p>
                                <a asp-controller="Accommodation" asp-action="Index" asp-route-locationId="@location.Id" class="btn btn-modern btn-modern-primary mt-2">Khám phá</a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12 text-center">
                <p>Chưa có địa điểm nổi bật.</p>
            </div>
        }
                </div>
            </div>

            <!-- Accommodations Tab Content -->
            <div class="tab-pane fade" id="accommodations-content" role="tabpanel" aria-labelledby="accommodations-main-tab">
                <div class="section-title" data-aos="fade-up">
                    <h2>Khách sạn đề xuất</h2>
                </div>
    <div class="row">
        @if (ViewBag.FeaturedAccommodations != null)
        {
            int accommodationIndex = 0;
            foreach (var accommodation in ViewBag.FeaturedAccommodations)
            {
                <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="@(accommodationIndex * 100)">
                    <div class="modern-card h-100">
                        <img src="@(string.IsNullOrEmpty(accommodation.ImageUrl) ? "/images/default-hotel.jpg" : accommodation.ImageUrl)"
                             class="card-img-top" style="height: 200px; object-fit: cover;" alt="@accommodation.Name">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h5 class="card-title mb-0">@accommodation.Name</h5>
                                <div>
                                    @for (int i = 0; i < accommodation.StarRating; i++)
                                    {
                                        <i class="bi bi-star-fill text-warning"></i>
                                    }
                                </div>
                            </div>
                            <p class="card-text">@(accommodation.Description?.Length > 100 ? accommodation.Description.Substring(0, 100) + "..." : accommodation.Description)</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-success fw-bold">Từ @accommodation.MinPrice.ToString("#,##0") VNĐ</span>
                                <a asp-controller="Accommodation" asp-action="Details" asp-route-id="@accommodation.Id" class="btn btn-modern btn-modern-outline">Xem chi tiết</a>
                            </div>
                        </div>
                    </div>
                </div>
                accommodationIndex++;
            }
        }
        else
        {
            <div class="col-12 text-center">
                <p>Chưa có khách sạn nổi bật.</p>
            </div>
        }
                </div>
            </div>

            <!-- Tours Tab Content -->
            <div class="tab-pane fade" id="tours-content" role="tabpanel" aria-labelledby="tours-main-tab">
                <div class="section-title" data-aos="fade-up">
                    <h2>Tour du lịch đề xuất</h2>
                </div>

    <!-- Featured Tour Banner -->
    <div class="row mb-4">
        <div class="col-12" data-aos="fade-up" data-aos-delay="100">
            <div class="position-relative featured-tour-banner rounded overflow-hidden">
                <img src="/images/tours/banners/tour_kham-pha-ben-tre_banner.jpg" class="img-fluid w-100" style="height: 350px; object-fit: cover;" alt="Tour Khám Phá Bến Tre">
                <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center" style="background: linear-gradient(to right, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.1) 100%);">
                    <div class="container-fluid px-4 px-md-5">
                        <div class="row">
                            <div class="col-md-7">
                                <span class="badge bg-warning mb-2 animate__animated animate__fadeInDown">Tour nổi bật</span>
                                <h2 class="text-white mb-3 text-shadow animate__animated animate__fadeInUp">Tour Khám Phá Bến Tre 2 Ngày 1 Đêm</h2>
                                <p class="text-white mb-3 animate__animated animate__fadeInUp animate__delay-1s">Trải nghiệm vẻ đẹp sông nước miền Tây, thưởng thức ẩm thực đặc sắc và khám phá văn hóa địa phương</p>
                                <div class="d-flex flex-wrap text-white mb-3 animate__animated animate__fadeInUp animate__delay-2s">
                                    <div class="me-4 mb-2"><i class="bi bi-clock me-2"></i> 2 ngày 1 đêm</div>
                                    <div class="me-4 mb-2"><i class="bi bi-people-fill me-2"></i> Tối đa 20 người</div>
                                    <div class="mb-2"><i class="bi bi-star-fill text-warning me-2"></i> 4.8/5 (120 đánh giá)</div>
                                </div>
                                <div class="d-flex align-items-center animate__animated animate__fadeInUp animate__delay-3s">
                                    <span class="text-white me-3 fs-4 fw-bold">1,500,000 VNĐ</span>
                                    <a href="#" class="btn btn-modern btn-warning">Đặt ngay</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Regular Tours Grid -->
    <div class="row">
        @if (ViewBag.FeaturedTours != null && ViewBag.FeaturedTours.Count > 0)
        {
            int tourIndex = 0;
            foreach (var tour in ViewBag.FeaturedTours)
            {
                <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="@(tourIndex * 100)">
                    <div class="modern-card h-100 tour-card">
                        <div class="position-relative">
                            <img src="@(string.IsNullOrEmpty(tour.ImageUrl) ? "/images/default-tour.jpg" : tour.ImageUrl)"
                                 class="card-img-top" style="height: 200px; object-fit: cover;" alt="@tour.Name">
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-primary">Tour mới</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title mb-2">@tour.Name</h5>
                            <p class="card-text text-muted mb-2">
                                <i class="bi bi-geo-alt-fill"></i> @tour.Location?.Name, @tour.City?.Name
                            </p>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>
                                    <i class="bi bi-clock"></i> @tour.Duration ngày
                                </span>
                                <span>
                                    <i class="bi bi-people-fill"></i> Tối đa @tour.MaxGroupSize người
                                </span>
                            </div>
                            <p class="card-text">@(tour.Description?.Length > 100 ? tour.Description.Substring(0, 100) + "..." : tour.Description)</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="text-success fw-bold">@tour.Price.ToString("#,##0") VNĐ</span>
                                <a asp-controller="Tour" asp-action="Details" asp-route-id="@tour.Id" class="btn btn-modern btn-modern-outline">Xem chi tiết</a>
                            </div>
                        </div>
                    </div>
                </div>
                tourIndex++;
            }
        }
        else
        {
            <div class="col-12 text-center">
                <p>Chưa có tour nổi bật.</p>
            </div>
        }
    </div>
                <div class="text-center mt-3">
                    <a asp-controller="Tour" asp-action="Index" class="btn btn-modern btn-modern-primary">Xem tất cả tour</a>
                </div>
            </div>

            <!-- Experiences Tab Content -->
            <div class="tab-pane fade" id="experiences-content" role="tabpanel" aria-labelledby="experiences-main-tab">
                <!-- Du lịch 360° -->
                <div class="section-title" data-aos="fade-up">
                    <h2>Du lịch 360° - Trải nghiệm ảo</h2>
                </div>
    <div class="row mb-4">
        <div class="col-12" data-aos="fade-up" data-aos-delay="100">
            <div class="position-relative featured-panorama-banner rounded overflow-hidden">
                <div class="panorama-banner" style="height: 350px; background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url('/images/banners/hinh-anh-trung-tam-thanh-pho-ben-tre-nhin-tu-tren-cao_022746613.jpg') center/cover no-repeat; border-radius: 8px;"></div>
                <div class="position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center" style="background: linear-gradient(to right, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.1) 100%);">
                    <div class="container-fluid px-4 px-md-5">
                        <div class="row">
                            <div class="col-md-7">
                                <span class="badge bg-info mb-2 animate__animated animate__fadeInDown">Tính năng mới</span>
                                <h2 class="text-white mb-3 text-shadow animate__animated animate__fadeInUp">Khám phá Bến Tre qua góc nhìn 360°</h2>
                                <p class="text-white mb-3 animate__animated animate__fadeInUp animate__delay-1s">Trải nghiệm không gian và cảnh quan của các địa điểm du lịch nổi tiếng trước khi quyết định đến tham quan</p>
                                <div class="d-flex align-items-center animate__animated animate__fadeInUp animate__delay-2s">
                                    <a asp-controller="Panorama360" asp-action="Index" class="btn btn-modern btn-info me-3">
                                        <i class="fas fa-vr-cardboard me-2"></i>Khám phá ngay
                                    </a>
                                    <a asp-controller="Panorama360" asp-action="Guide" class="btn btn-modern btn-outline-light">
                                        <i class="fas fa-question-circle me-2"></i>Hướng dẫn sử dụng
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                </div>

                <!-- Trải nghiệm du lịch Bến Tre -->
                <div class="section-title mt-5" data-aos="fade-up">
                    <h2>Trải nghiệm du lịch Bến Tre</h2>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-4" data-aos="fade-up">
                        <div class="modern-card h-100 text-center p-4">
                            <div class="mb-3">
                                <i class="bi bi-water text-primary" style="font-size: 3rem;"></i>
                            </div>
                            <h5>Du lịch sông nước</h5>
                            <p>Trải nghiệm đi thuyền trên sông, khám phá các cồn, lách và các làng nghề truyền thống dọc hai bên bờ sông.</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                        <div class="modern-card h-100 text-center p-4">
                            <div class="mb-3">
                                <i class="bi bi-tree text-primary" style="font-size: 3rem;"></i>
                            </div>
                            <h5>Khám phá vườn trái cây</h5>
                            <p>Tham quan các vườn trái cây nhiệt đới, thưởng thức trái cây tươi ngon và tìm hiểu về cuộc sống của người dân địa phương.</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                        <div class="modern-card h-100 text-center p-4">
                            <div class="mb-3">
                                <i class="bi bi-cup-hot text-primary" style="font-size: 3rem;"></i>
                            </div>
                            <h5>Ẩm thực đặc sắc</h5>
                            <p>Thưởng thức các món ăn đặc sản của Bến Tre như cá lóc nướng trui, chuột đồng nướng, các món từ dừa và nhiều món ngon khác.</p>
                        </div>
                    </div>
                </div>

                <!-- AI Recommendation Promo -->
                <div class="section-title mt-5" data-aos="fade-up">
                    <h2>AI Gợi Ý Lịch Trình</h2>
                </div>
                <div class="modern-card border-0 rounded-lg overflow-hidden">
                    <div class="row g-0">
                        <div class="col-md-6">
                            <div class="bg-primary text-white p-5 h-100 d-flex flex-column justify-content-center" data-aos="fade-right">
                                <span class="badge bg-warning mb-3">Tính năng mới</span>
                                <h2 class="mb-3">AI Gợi Ý Lịch Trình Du Lịch</h2>
                                <p class="mb-4">Để AI giúp bạn lên kế hoạch du lịch Bến Tre phù hợp với sở thích, ngân sách và thời gian của bạn. Chỉ với vài bước đơn giản, bạn sẽ có ngay lịch trình du lịch cá nhân hóa!</p>
                                <div>
                                    <a asp-controller="Recommendation" asp-action="SimpleForm" class="btn btn-modern btn-light btn-lg">
                                        <i class="bi bi-magic me-2"></i>Tạo lịch trình ngay
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="position-relative h-100" data-aos="fade-left">
                                <img src="/images/banners/ai-recommendation.jpg" class="w-100 h-100" style="object-fit: cover;" alt="AI Recommendation">
                                <div class="position-absolute top-50 start-50 translate-middle">
                                    <a asp-controller="Recommendation" asp-action="SimpleForm" class="btn btn-modern btn-modern-primary btn-lg rounded-circle">
                                        <i class="bi bi-arrow-right-circle-fill fs-1"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- About Tab Content -->
            <div class="tab-pane fade" id="about-content" role="tabpanel" aria-labelledby="about-main-tab">
                <!-- Giới thiệu về Bến Tre -->
                <div class="section-title" data-aos="fade-up">
                    <h2>Khám phá Bến Tre</h2>
                </div>
    <div class="row align-items-center mb-4">
        <div class="col-md-6 mb-4" data-aos="fade-right">
            <img src="/images/banners/banner_home_01.jpg" class="img-fluid rounded shadow" alt="Bến Tre - Xứ Dừa">
        </div>
        <div class="col-md-6 mb-4" data-aos="fade-left">
            <h3 class="mb-3">Vùng đất của dừa và sông nước</h3>
            <p>Bến Tre được mệnh danh là "Xứ Dừa" với hơn 72.000 hecta dừa, chiếm 1/3 diện tích dừa cả nước. Đến với Bến Tre, du khách sẽ được đắm mình trong không gian xanh mát của những vườn dừa bạt ngàn, thưởng thức hương vị ngọt ngào của nước dừa tươi và khám phá các sản phẩm thủ công mỹ nghệ được làm từ dừa.</p>
            <p>Bến Tre còn là vùng đất của sông nước với hệ thống sông ngòi chằng chịt, tạo nên cảnh quan thiên nhiên tuyệt đẹp và là nơi sinh sống của nhiều loài động thực vật đặc trưng vùng sông nước miền Tây.</p>
            <a asp-controller="Explore" asp-action="BenTre" class="btn btn-modern btn-modern-primary">Tìm hiểu thêm</a>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-4 mb-4" data-aos="fade-up">
            <div class="modern-card h-100 p-4 text-center">
                <div class="mb-3">
                    <i class="bi bi-geo-alt text-primary" style="font-size: 2.5rem;"></i>
                </div>
                <h5 class="mb-3">Vị trí địa lý</h5>
                <p>Bến Tre nằm ở phía Đông Nam của đồng bằng sông Cửu Long, cách TP. Hồ Chí Minh khoảng 85km về phía Nam. Tỉnh có diện tích 2.360 km² với dân số khoảng 1,3 triệu người.</p>
            </div>
        </div>
        <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="100">
            <div class="modern-card h-100 p-4 text-center">
                <div class="mb-3">
                    <i class="bi bi-clock-history text-primary" style="font-size: 2.5rem;"></i>
                </div>
                <h5 class="mb-3">Lịch sử văn hóa</h5>
                <p>Bến Tre có lịch sử hình thành và phát triển lâu đời, là quê hương của nhiều danh nhân, anh hùng dân tộc. Nơi đây còn lưu giữ nhiều di tích lịch sử, văn hóa có giá trị.</p>
            </div>
        </div>
        <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
            <div class="modern-card h-100 p-4 text-center">
                <div class="mb-3">
                    <i class="bi bi-calendar-event text-primary" style="font-size: 2.5rem;"></i>
                </div>
                <h5 class="mb-3">Thời điểm lý tưởng</h5>
                <p>Thời gian lý tưởng để du lịch Bến Tre là từ tháng 12 đến tháng 4 năm sau, khi thời tiết mát mẻ, ít mưa. Đặc biệt vào mùa khô, bạn sẽ có cơ hội tham gia nhiều lễ hội địa phương.</p>
            </div>
        </div>
    </div>

                <div class="row mt-3">
                    <div class="col-12 text-center">
                        <a asp-controller="About" asp-action="Index" class="btn btn-modern btn-modern-primary">Tìm hiểu thêm về Bến Tre</a>
                    </div>
                </div>

                <!-- Newsletter -->
                <div class="section-title mt-5" data-aos="fade-up">
                    <h2>Đăng ký nhận thông tin</h2>
                </div>
                <div class="modern-card bg-light">
                    <div class="card-body p-4 text-center">
                        <h3>Đăng ký nhận thông tin ưu đãi</h3>
                        <p>Nhận thông tin về các ưu đãi và khuyến mãi mới nhất từ chúng tôi.</p>
                        <div class="row justify-content-center">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="email" class="form-control" placeholder="Nhập email của bạn">
                                    <button class="btn btn-modern btn-modern-primary" type="button">Đăng ký</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
